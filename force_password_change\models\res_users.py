# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta


class ResUsers(models.Model):
    _inherit = 'res.users'

    must_change_password = fields.Boolean(
        string='Must Change Password',
        default=True,
        help='If checked, the user will be forced to change their password on next login'
    )

    password_last_changed = fields.Datetime(
        string='Password Last Changed',
        help='Date and time when the password was last changed'
    )

    password_expiry_days = fields.Integer(
        string='Password Expiry Days',
        default=30,
        help='Number of days after which password expires (0 = never expires)'
    )

    password_expires_on = fields.Datetime(
        string='Password Expires On',
        compute='_compute_password_expires_on',
        store=True,
        help='Date and time when the password will expire'
    )

    password_expired = fields.Boolean(
        string='Password Expired',
        compute='_compute_password_expired',
        help='True if the password has expired'
    )

    @api.depends('password_last_changed', 'password_expiry_days')
    def _compute_password_expires_on(self):
        """Compute when the password will expire"""
        for user in self:
            if user.password_last_changed and user.password_expiry_days > 0:
                user.password_expires_on = user.password_last_changed + timedelta(days=user.password_expiry_days)
            else:
                user.password_expires_on = False

    @api.depends('password_expires_on')
    def _compute_password_expired(self):
        """Check if password has expired"""
        now = fields.Datetime.now()
        for user in self:
            if user.password_expires_on and user.password_expires_on <= now:
                user.password_expired = True
            else:
                user.password_expired = False

    @api.model
    def create(self, vals):
        """Override create to set must_change_password to True for new users"""
        # Set must_change_password to True by default for new users
        if 'must_change_password' not in vals:
            vals['must_change_password'] = True

        # Set password creation time if password is provided
        if 'password' in vals and vals.get('password'):
            vals['password_last_changed'] = fields.Datetime.now()

        user = super(ResUsers, self).create(vals)
        return user

    def write(self, vals):
        """Override write to handle password changes"""
        # If password is being changed, set must_change_password to False
        if 'password' in vals and vals.get('password'):
            # Only set to False if the user is changing their own password
            # and they currently have must_change_password = True
            if self.env.context.get('user_changing_own_password'):
                vals['must_change_password'] = False
        
        return super(ResUsers, self).write(vals)

    def change_password(self, old_passwd, new_passwd):
        """Override change_password to mark password as changed"""
        # Set context to indicate user is changing their own password
        self = self.with_context(user_changing_own_password=True)
        result = super(ResUsers, self).change_password(old_passwd, new_passwd)

        # Update password change tracking
        now = fields.Datetime.now()
        update_vals = {
            'must_change_password': False,
            'password_last_changed': now,
        }

        self.sudo().write(update_vals)

        return result

    def _set_password(self):
        """Override _set_password to handle password changes"""
        # If password is being changed and user is changing their own password
        if self.env.context.get('user_changing_own_password'):
            # Call parent method first
            super(ResUsers, self)._set_password()
            # Then update must_change_password flag
            for user in self:
                if user.must_change_password:
                    user.sudo().write({'must_change_password': False})
        else:
            super(ResUsers, self)._set_password()

    @api.model
    def check_password_change_required(self, user_id):
        """Check if user needs to change password"""
        user = self.browse(user_id)
        if not user.exists():
            return False

        # Check if password change is manually required
        if user.must_change_password:
            return True

        # Check if password has expired
        if user.password_expired:
            # Automatically set must_change_password if password expired
            user.sudo().write({'must_change_password': True})
            return True

        return False

    @api.model
    def cron_check_password_expiry(self):
        """Cron job to check and mark expired passwords"""
        users = self.search([
            ('active', '=', True),
            ('password_expiry_days', '>', 0),
            ('password_last_changed', '!=', False),
            ('must_change_password', '=', False)
        ])

        expired_count = 0
        for user in users:
            if user.password_expired:
                user.sudo().write({'must_change_password': True})
                expired_count += 1

        if expired_count > 0:
            self.env['ir.logging'].sudo().create({
                'name': 'force_password_change',
                'type': 'server',
                'level': 'INFO',
                'message': f'Marked {expired_count} users for password change due to expiry',
                'path': 'force_password_change.models.res_users',
                'func': 'cron_check_password_expiry',
            })

        return expired_count
