odoo.define('force_password_change.password_change', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');

    publicWidget.registry.ForcePasswordChangeWidget = publicWidget.Widget.extend({
        selector: 'body',

        start: function () {
            var self = this;

            // Check if we're on the password change page
            if (window.location.pathname === '/force_password_change/change_password') {
                // Check if password was already submitted (prevent back button access)
                if (sessionStorage.getItem('password_change_submitted') === 'true') {
                    sessionStorage.removeItem('password_change_submitted');
                    window.location.replace('/web');
                    return;
                }

                // Add class to body for CSS targeting
                document.body.classList.add('force-password-change-page');

                // Prevent back button access to this page
                this._preventBackButtonAccess();

                // Hide "My Account" link immediately
                this._hideMyAccountLink();

                // Monitor for dynamic content changes (user dropdown loading)
                var observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            // Check if new dropdown items were added
                            var addedNodes = Array.from(mutation.addedNodes);
                            var hasDropdownItems = addedNodes.some(function(node) {
                                return node.nodeType === 1 && (
                                    node.classList.contains('dropdown-item') ||
                                    node.querySelector && node.querySelector('.dropdown-item')
                                );
                            });

                            if (hasDropdownItems) {
                                setTimeout(function() {
                                    self._hideMyAccountLink();
                                }, 100);
                            }
                        }
                    });
                });

                // Start observing
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // Also hide on dropdown show events
                $(document).on('shown.bs.dropdown', function() {
                    self._hideMyAccountLink();
                });
            }

            return this._super.apply(this, arguments);
        },

        _preventBackButtonAccess: function () {
            // Method 1: Replace current history entry
            if (window.history && window.history.replaceState) {
                window.history.replaceState(null, null, window.location.href);
            }

            // Method 2: Add event listener for popstate (back button)
            window.addEventListener('popstate', function() {
                // Redirect to main page if user tries to go back
                window.location.replace('/web');
            });

            // Method 3: Push a dummy state to prevent going back to previous page
            if (window.history && window.history.pushState) {
                window.history.pushState(null, null, window.location.href);
                window.addEventListener('popstate', function() {
                    window.location.replace('/web');
                });
            }

            // Method 4: Disable back button using onbeforeunload
            window.onbeforeunload = function() {
                return "Are you sure you want to leave this page?";
            };

            // Method 5: Override browser back functionality
            $(document).ready(function() {
                // Disable browser back button
                window.history.forward();

                // Handle form submission to remove onbeforeunload and prevent back access
                $('form[action="/force_password_change/submit_password"]').on('submit', function() {
                    window.onbeforeunload = null;

                    // Set flag in sessionStorage to prevent back access
                    sessionStorage.setItem('password_change_submitted', 'true');

                    // Replace current page in history to prevent back button
                    if (window.history && window.history.replaceState) {
                        window.history.replaceState(null, null, '/web');
                    }
                });
            });
        },

        _hideMyAccountLink: function () {
            // Method 1: Hide by href attribute
            var myAccountLinks = document.querySelectorAll('a[href="/my/home"]');
            myAccountLinks.forEach(function(link) {
                link.style.display = 'none';
            });

            // Method 2: Hide by text content
            var dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(function(item) {
                var text = item.textContent.trim();
                if (text === 'My Account' || text.includes('My Account')) {
                    item.style.display = 'none';
                }
            });

            // Method 3: Hide by Arabic text (if applicable)
            dropdownItems.forEach(function(item) {
                var text = item.textContent.trim();
                if (text.includes('حسابي') || text.includes('الحساب')) {
                    item.style.display = 'none';
                }
            });
        }
    });

    return publicWidget.registry.ForcePasswordChangeWidget;
});
