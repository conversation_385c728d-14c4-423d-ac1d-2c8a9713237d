odoo.define('force_password_change.password_change', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');

    publicWidget.registry.ForcePasswordChangeWidget = publicWidget.Widget.extend({
        selector: 'body.force-password-change-page',

        start: function () {
            var self = this;

            // Add class to body for CSS targeting
            document.body.classList.add('force-password-change-page');

            // Hide "My Account" link immediately
            this._hideMyAccountLink();

            // Monitor for dynamic content changes (user dropdown loading)
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        // Check if new dropdown items were added
                        var addedNodes = Array.from(mutation.addedNodes);
                        var hasDropdownItems = addedNodes.some(function(node) {
                            return node.nodeType === 1 && (
                                node.classList.contains('dropdown-item') ||
                                node.querySelector && node.querySelector('.dropdown-item')
                            );
                        });

                        if (hasDropdownItems) {
                            setTimeout(function() {
                                self._hideMyAccountLink();
                            }, 100);
                        }
                    }
                });
            });

            // Start observing
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Also hide on dropdown show events
            $(document).on('shown.bs.dropdown', function() {
                self._hideMyAccountLink();
            });

            return this._super.apply(this, arguments);
        },

        _hideMyAccountLink: function () {
            // Method 1: Hide by href attribute
            var myAccountLinks = document.querySelectorAll('a[href="/my/home"]');
            myAccountLinks.forEach(function(link) {
                link.style.display = 'none';
            });

            // Method 2: Hide by text content
            var dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(function(item) {
                var text = item.textContent.trim();
                if (text === 'My Account' || text.includes('My Account')) {
                    item.style.display = 'none';
                }
            });

            // Method 3: Hide by Arabic text (if applicable)
            dropdownItems.forEach(function(item) {
                var text = item.textContent.trim();
                if (text.includes('حسابي') || text.includes('الحساب')) {
                    item.style.display = 'none';
                }
            });
        }
    });

    return publicWidget.registry.ForcePasswordChangeWidget;
});
