# -*- coding: utf-8 -*-
{
    'name': 'Force Password Change',
    'version': '********.0',
    'category': 'Authentication',
    'summary': 'Force new users to change their password on first login',
    'description': """
Force Password Change for New Users
===================================

This module forces new users to change their password when they log in for the first time.

Features:
---------
* Automatically sets must_change_password flag to True for new users
* Redirects users to password change form on first login
* Clear warning message for users
* Seamless integration with Odoo's authentication system
* Works with Odoo 15 Community Edition

Usage:
------
1. Install the module
2. Create new users from Settings > Users
3. New users will be forced to change their password on first login
4. After password change, users can access the system normally
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'web',
        'auth_signup',
        'portal',
    ],
    'data': [
        'data/default_data.xml',
        'security/ir.model.access.csv',
        'views/res_users_views.xml',
        'views/password_change_views.xml',
        'views/templates.xml',
    ],
    'demo': [
        'demo/demo_users.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'force_password_change/static/src/css/password_change.css',
            'force_password_change/static/src/js/password_change.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
