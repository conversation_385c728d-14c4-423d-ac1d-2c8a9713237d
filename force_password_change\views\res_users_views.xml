<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add must_change_password field to user form -->
        <record id="view_users_form_inherit" model="ir.ui.view">
            <field name="name">res.users.form.inherit.force.password.change</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='active']" position="after">
                    <field name="must_change_password"
                           attrs="{'invisible': [('id', '=', False)]}"
                           help="If checked, the user will be forced to change their password on next login"/>
                    <field name="password_expiry_days"
                           attrs="{'invisible': [('id', '=', False)]}"
                           help="Number of days after which password expires (0 = never expires)"/>
                    <field name="password_last_changed"
                           attrs="{'invisible': [('id', '=', False)]}"
                           readonly="1"
                           help="Date and time when the password was last changed"/>
                    <field name="password_expires_on"
                           attrs="{'invisible': [('id', '=', False)]}"
                           readonly="1"
                           help="Date and time when the password will expire"/>
                    <field name="password_expired"
                           attrs="{'invisible': [('id', '=', False)]}"
                           readonly="1"
                           help="True if the password has expired"/>
                </xpath>
            </field>
        </record>

        <!-- Add must_change_password field to user tree view -->
        <record id="view_users_tree_inherit" model="ir.ui.view">
            <field name="name">res.users.tree.inherit.force.password.change</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="inside">
                    <field name="must_change_password" string="Must Change Password"/>
                    <field name="password_expiry_days" string="Password Expiry Days"/>
                    <field name="password_last_changed" string="Password Last Changed"/>
                    <field name="password_expires_on" string="Password Expires On"/>
                    <field name="password_expired" string="Password Expired"/>
                </xpath>
            </field>
        </record>

        <!-- Add must_change_password field to user search view -->
        <record id="view_users_search_inherit" model="ir.ui.view">
            <field name="name">res.users.search.inherit.force.password.change</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_search"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <separator/>
                    <filter name="must_change_password"
                            string="Must Change Password"
                            domain="[('must_change_password', '=', True)]"/>
                    <filter name="password_expired"
                            string="Password Expired"
                            domain="[('password_expired', '=', True)]"/>
                    <filter name="password_expiring_soon"
                            string="Password Expiring Soon (7 days)"
                            domain="[('password_expires_on', '&lt;=', (context_today() + datetime.timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')), ('password_expires_on', '&gt;', context_today().strftime('%Y-%m-%d %H:%M:%S'))]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
