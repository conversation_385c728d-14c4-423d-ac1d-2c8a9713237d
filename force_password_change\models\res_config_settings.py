# -*- coding: utf-8 -*-

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    force_password_change_enabled = fields.Boolean(
        string='Enable Force Password Change',
        config_parameter='force_password_change.enabled',
        default=True,
        help='Enable forcing users to change their password'
    )
    
    force_password_change_min_length = fields.Integer(
        string='Minimum Password Length',
        config_parameter='force_password_change.min_length',
        default=6,
        help='Minimum required password length'
    )
    
    force_password_change_enable_expiry = fields.Boolean(
        string='Enable Password Expiry',
        config_parameter='force_password_change.enable_expiry',
        default=True,
        help='Enable automatic password expiry'
    )
    
    force_password_change_default_expiry_days = fields.Integer(
        string='Default Password Expiry Days',
        config_parameter='force_password_change.default_expiry_days',
        default=30,
        help='Default number of days after which passwords expire (0 = never expires)'
    )

    @api.model
    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        res.update(
            force_password_change_enabled=ICPSudo.get_param('force_password_change.enabled', True),
            force_password_change_min_length=int(ICPSudo.get_param('force_password_change.min_length', 6)),
            force_password_change_enable_expiry=ICPSudo.get_param('force_password_change.enable_expiry', True),
            force_password_change_default_expiry_days=int(ICPSudo.get_param('force_password_change.default_expiry_days', 30)),
        )
        return res

    def set_values(self):
        super(ResConfigSettings, self).set_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        ICPSudo.set_param('force_password_change.enabled', self.force_password_change_enabled)
        ICPSudo.set_param('force_password_change.min_length', self.force_password_change_min_length)
        ICPSudo.set_param('force_password_change.enable_expiry', self.force_password_change_enable_expiry)
        ICPSudo.set_param('force_password_change.default_expiry_days', self.force_password_change_default_expiry_days)
