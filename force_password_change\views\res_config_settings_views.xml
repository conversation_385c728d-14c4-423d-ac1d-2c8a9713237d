<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add Force Password Change settings to General Settings -->
        <record id="res_config_settings_view_form_inherit" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.force.password.change</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('settings')]" position="inside">
                    <div class="app_settings_block" data-string="Force Password Change" string="Force Password Change" data-key="force_password_change">
                        <h2>Force Password Change</h2>
                        
                        <div class="row mt16 o_settings_container">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="force_password_change_enabled"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="force_password_change_enabled"/>
                                    <div class="text-muted">
                                        Force users to change their password on first login or when required
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-lg-6 o_setting_box" attrs="{'invisible': [('force_password_change_enabled', '=', False)]}">
                                <div class="o_setting_left_pane">
                                    <field name="force_password_change_enable_expiry"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="force_password_change_enable_expiry"/>
                                    <div class="text-muted">
                                        Enable automatic password expiry after a specified number of days
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt16 o_settings_container" attrs="{'invisible': [('force_password_change_enabled', '=', False)]}">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <label for="force_password_change_min_length" string="Minimum Password Length"/>
                                    <field name="force_password_change_min_length" class="o_light_label"/>
                                    <div class="text-muted">
                                        Minimum number of characters required for passwords
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-lg-6 o_setting_box" attrs="{'invisible': [('force_password_change_enable_expiry', '=', False)]}">
                                <div class="o_setting_right_pane">
                                    <label for="force_password_change_default_expiry_days" string="Default Password Expiry Days"/>
                                    <field name="force_password_change_default_expiry_days" class="o_light_label"/>
                                    <div class="text-muted">
                                        Default number of days after which passwords expire (0 = never expires)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
