# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def post_init_hook(cr, registry):
    """Post-installation hook to set up initial data"""
    env = api.Environment(cr, SUPERUSER_ID, {})

    # Log installation
    _logger.info("Force Password Change module installed successfully")

    # Get default expiry days from config
    default_expiry_days = int(env['ir.config_parameter'].sudo().get_param(
        'force_password_change.default_expiry_days', '30'
    ))

    # Set password_last_changed for existing users who have passwords
    users_with_password = env['res.users'].search([
        ('password', '!=', False),
        ('active', '=', True),
        ('password_last_changed', '=', False),
        ('id', '!=', SUPERUSER_ID)
    ])

    if users_with_password:
        # Set password_last_changed to now for existing users
        from odoo import fields
        now = fields.Datetime.now()
        users_with_password.write({
            'password_last_changed': now,
            'password_expiry_days': default_expiry_days,
        })
        _logger.info(f"Set password_last_changed for {len(users_with_password)} existing users")

    # Optionally set must_change_password for existing users without passwords
    users_without_password = env['res.users'].search([
        ('password', '=', False),
        ('active', '=', True),
        ('id', '!=', SUPERUSER_ID)
    ])

    if users_without_password:
        users_without_password.write({
            'must_change_password': True,
            'password_expiry_days': default_expiry_days,
        })
        _logger.info(f"Set must_change_password for {len(users_without_password)} users without passwords")


def uninstall_hook(cr, registry):
    """Pre-uninstallation hook to clean up data"""
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Reset must_change_password for all users
    users = env['res.users'].search([('must_change_password', '=', True)])
    if users:
        users.write({'must_change_password': False})
        _logger.info(f"Reset must_change_password for {len(users)} users")
    
    _logger.info("Force Password Change module uninstalled successfully")
