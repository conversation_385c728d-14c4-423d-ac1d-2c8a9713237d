<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- <PERSON>ron job to check password expiry -->
        <record id="cron_check_password_expiry" model="ir.cron">
            <field name="name">Check Password Expiry</field>
            <field name="model_id" ref="base.model_res_users"/>
            <field name="state">code</field>
            <field name="code">model.cron_check_password_expiry()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active" eval="True"/>
            <field name="doall" eval="False"/>
        </record>
    </data>
</odoo>
